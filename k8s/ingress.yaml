apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chollisimos
  namespace: production
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    # Redirigir de dominio apex (sin www) a www
    nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: clases.pl
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chollisimos
            port:
              number: 80
  - host: www.clases.pl
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chollisimos
            port:
              number: 80

  tls:
    - hosts:
      - clases.pl
      - www.clases.pl
      secretName: chollisimos-tls-secret    # Nombre del secreto que contiene el certificado TLS
