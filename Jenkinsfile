pipeline {
	agent any

    environment {

        // JENKINS pone de BUILD_TAG = jenkins-clases.pl-3 (ANALOGO)
        // es decir, ya incluye el nombre de job ("clases.pl") de la app en cuestión

        // Variables de configuración de DOCKER HUB
        DOCKER_HUB_USER_NAME = 'soria86'
        DOCKER_HUB_REPO_NAME = 'my_docker_hub_repo_for_all_my_apps'

        // El que hayas puesto en la configuración "k8s"
        K8S_DEPLOY_NAME = "chollisimos"

        // Variables de Docker y Registry
		DOCKER_REGISTRY = "docker.io/${DOCKER_HUB_USER_NAME}"
        DOCKER_CREDENTIALS = 'docker-credentials-id'

        // Variables de Kubernetes
        KUBECONFIG = credentials('kubeconfig-credentials-id')
        K8S_NAMESPACE = 'production'
    }

    stages {
		stage('Checkout') {
			steps {
				checkout scm
            }
        }

        stage('Obtener branch') {
			steps {
				script {
					echo "La rama actual es: ${env.GIT_BRANCH}"
                }
            }
        }

        stage('Build') {
			steps {
				withCredentials([usernamePassword(credentialsId: DOCKER_CREDENTIALS,
                                                 usernameVariable: 'DOCKER_USER',
                                                 passwordVariable: 'DOCKER_PASS')]) {

                    sh 'echo "$DOCKER_PASS" | docker login -u "$DOCKER_USER" --password-stdin $DOCKER_REGISTRY'

                    sh 'docker build -t $DOCKER_HUB_REPO_NAME:$BUILD_TAG .'
                    sh 'docker tag $DOCKER_HUB_REPO_NAME:$BUILD_TAG $DOCKER_HUB_USER_NAME/$DOCKER_HUB_REPO_NAME:$BUILD_TAG'
                }
            }
        }

        stage('Push') {
			steps {
				sh 'docker push $DOCKER_REGISTRY/$DOCKER_HUB_REPO_NAME:$BUILD_TAG'
            }
        }

        stage('Deploy to Kubernetes') {
			steps {
				withCredentials([usernamePassword(credentialsId: DOCKER_CREDENTIALS,
                                                 usernameVariable: 'DOCKER_USER',
                                                 passwordVariable: 'DOCKER_PASS')]) {

					sh """
							export KUBECONFIG=\${KUBECONFIG}

							# Crear o actualizar el secreto de Docker Hub
							kubectl create secret docker-registry docker-credentials \\
							  --docker-server=\${DOCKER_REGISTRY%/*} \\
							  --docker-username="\${DOCKER_USER}" \\
							  --docker-password="\${DOCKER_PASS}" \\
							  --namespace \${K8S_NAMESPACE} \\
							  --dry-run=client -o yaml | kubectl apply -f -

                           # Crear secreto de PostgreSQL
                           kubectl apply -f k8s/postgresql/postgresql-secret.yaml -n $K8S_NAMESPACE

							# Eliminar pods antiguos que puedan estar bloqueados
							kubectl delete pod -l app=$K8S_DEPLOY_NAME -n $K8S_NAMESPACE

							# Esperar a que se eliminen los pods'
							sleep 10

							# Verificar que no hay pods en ejecución'
							kubectl get pods -n $K8S_NAMESPACE -l app=$K8S_DEPLOY_NAME

							# Esperar a que se eliminen los recursos'
							sleep 5

							# Levantar PostgreSQL
                            kubectl apply -f k8s/postgresql/postgresql-pv-pvc.yaml
                            kubectl apply -f k8s/postgresql/postgresql-service.yaml -n $K8S_NAMESPACE
                            # !! Este crea el pod postgresql-0 (namespace $K8S_NAMESPACE)
                            kubectl apply -f k8s/postgresql/postgresql-statefulset.yaml -n $K8S_NAMESPACE

							# Actualizar imagen en deployment'
							sed -i 's|\${DOCKER_REGISTRY}|${DOCKER_REGISTRY}|g' k8s/deployment.yaml
							sed -i 's|\${BUILD_TAG}|${BUILD_TAG}|g' k8s/deployment.yaml

							kubectl apply -f k8s/metallb.yaml

							# Aplicar deployment y service'
							kubectl apply -f k8s/deployment.yaml
							kubectl apply -f k8s/service.yaml
							kubectl apply -f k8s/ingress-single.yaml
							kubectl apply -f k8s/postgresql/postgresql-external.yaml -n $K8S_NAMESPACE
							kubectl apply -f k8s/prod_issuer.yaml
							kubectl apply -f k8s/custom-certificate.yaml


							# Reiniciar el deployment para forzar pull de la imagen'
							# Reiniciar pods:
							kubectl rollout restart deployment $K8S_DEPLOY_NAME -n $K8S_NAMESPACE

							"""

				}
            }
        }
    }
    
    post {
		always {
			cleanWs()
        }
        success {
			echo 'Pipeline ejecutado exitosamente!'
        }
        failure {
			echo 'El pipeline ha fallado'
        }
    }
} 